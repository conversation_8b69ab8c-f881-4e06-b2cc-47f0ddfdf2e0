{"cells": [{"cell_type": "code", "execution_count": 1, "id": "73eeb778", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New https://pypi.org/project/ultralytics/8.3.170 available  Update with 'pip install -U ultralytics'\n", "Ultralytics 8.3.141  Python-3.12.5 torch-2.6.0+cu118 CUDA:0 (NVIDIA GeForce RTX 3070 Ti Laptop GPU, 8192MiB)\n", "\u001b[34m\u001b[1mengine\\trainer: \u001b[0magnostic_nms=False, amp=False, augment=False, auto_augment=randaugment, batch=32, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=C:\\Users\\<USER>\\Desktop\\Disease_prime, degrees=0.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=100, erasing=0.4, exist_ok=False, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=224, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=yolov8l-cls.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=train3, nbs=64, nms=False, opset=None, optimize=False, optimizer=auto, overlap_mask=True, patience=100, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=None, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=runs\\classify\\train3, save_frames=False, save_json=False, save_period=-1, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=classify, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3.0, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None\n", "\u001b[34m\u001b[1mtrain:\u001b[0m C:\\Users\\<USER>\\Desktop\\Disease_prime\\train... found 22308 images in 31 classes  \n", "\u001b[34m\u001b[1mval:\u001b[0m C:\\Users\\<USER>\\Desktop\\Disease_prime\\val... found 1874 images in 31 classes  \n", "\u001b[34m\u001b[1mtest:\u001b[0m C:\\Users\\<USER>\\Desktop\\Disease_prime\\test... found 950 images in 31 classes  \n", "Overriding model.yaml nc=1000 with nc=31\n", "\n", "                   from  n    params  module                                       arguments                     \n", "  0                  -1  1      1856  ultralytics.nn.modules.conv.Conv             [3, 64, 3, 2]                 \n", "  1                  -1  1     73984  ultralytics.nn.modules.conv.Conv             [64, 128, 3, 2]               \n", "  2                  -1  3    279808  ultralytics.nn.modules.block.C2f             [128, 128, 3, True]           \n", "  3                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]              \n", "  4                  -1  6   2101248  ultralytics.nn.modules.block.C2f             [256, 256, 6, True]           \n", "  5                  -1  1   1180672  ultralytics.nn.modules.conv.Conv             [256, 512, 3, 2]              \n", "  6                  -1  6   8396800  ultralytics.nn.modules.block.C2f             [512, 512, 6, True]           \n", "  7                  -1  1   4720640  ultralytics.nn.modules.conv.Conv             [512, 1024, 3, 2]             \n", "  8                  -1  3  17836032  ultralytics.nn.modules.block.C2f             [1024, 1024, 3, True]         \n", "  9                  -1  1   1352991  ultralytics.nn.modules.head.Classify         [1024, 31]                    \n", "YOLOv8l-cls summary: 104 layers, 36,239,455 parameters, 36,239,455 gradients, 99.2 GFLOPs\n", "Transferred 300/302 items from pretrained weights\n", "\u001b[34m\u001b[1mtrain: \u001b[0mFast image access  (ping: 0.00.0 ms, read: 458.7218.3 MB/s, size: 50.0 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mScanning C:\\Users\\<USER>\\Desktop\\Disease_prime\\train... 22308 images, 0 corrupt: 100%|██████████| 22308/22308 [00:00<?, ?it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[34m\u001b[1mval: \u001b[0mFast image access  (ping: 0.00.0 ms, read: 643.5237.8 MB/s, size: 67.8 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mval: \u001b[0mScanning C:\\Users\\<USER>\\Desktop\\Disease_prime\\val... 1874 images, 0 corrupt: 100%|██████████| 1874/1874 [00:00<00:00, 2591.13it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[34m\u001b[1mval: \u001b[0mNew cache created: C:\\Users\\<USER>\\Desktop\\Disease_prime\\val.cache\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[34m\u001b[1moptimizer:\u001b[0m 'optimizer=auto' found, ignoring 'lr0=0.01' and 'momentum=0.937' and determining best 'optimizer', 'lr0' and 'momentum' automatically... \n", "\u001b[34m\u001b[1moptimizer:\u001b[0m SGD(lr=0.01, momentum=0.9) with parameter groups 50 weight(decay=0.0), 51 weight(decay=0.0005), 51 bias(decay=0.0)\n", "Image sizes 224 train, 224 val\n", "Using 8 dataloader workers\n", "Logging results to \u001b[1mruns\\classify\\train3\u001b[0m\n", "Starting training for 100 epochs...\n", "\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      1/100      3.79G      1.865          4        224: 100%|██████████| 698/698 [02:04<00:00,  5.60it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:03<00:00,  9.89it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.755       0.97\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      2/100      3.95G     0.7199          4        224: 100%|██████████| 698/698 [01:57<00:00,  5.92it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.849      0.989\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      3/100      4.04G     0.4408          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.851      0.987\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      4/100      4.04G      0.342          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.12it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.865      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      5/100      4.04G     0.2655          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.46it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.891      0.992\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      6/100      4.04G     0.2074          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.12it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.895      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      7/100      4.04G     0.1751          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.12it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.907      0.992\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      8/100      4.04G     0.1541          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.912      0.991\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      9/100      4.04G     0.1397          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.911      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     10/100      4.04G     0.1376          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.905      0.991\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     11/100      4.04G     0.1236          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.914       0.99\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     12/100      4.04G     0.1156          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.08it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.913      0.992\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     13/100      4.04G     0.1169          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.12it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.915      0.991\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     14/100      4.04G     0.1086          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.908      0.988\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     15/100      4.04G     0.1026          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.914      0.989\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     16/100      4.04G     0.1027          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.51it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.916       0.99\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     17/100      4.04G     0.1001          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.51it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.918       0.99\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     18/100      4.04G    0.08897          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all       0.91       0.99\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     19/100      4.04G    0.09369          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.52it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.918       0.99\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     20/100      4.04G    0.09627          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.12it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.46it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all       0.93      0.991\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     21/100      4.04G    0.09157          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.49it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.917      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     22/100      4.04G    0.09966          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.54it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.919      0.991\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     23/100      4.04G    0.09438          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.46it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.919      0.992\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     24/100      4.04G    0.08865          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.922      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     25/100      4.04G    0.08463          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.49it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.919      0.991\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     26/100      4.04G    0.08566          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.51it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.924      0.992\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     27/100      4.04G    0.09335          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.45it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.918      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     28/100      4.04G    0.08783          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.55it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.922      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     29/100      4.04G     0.0841          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.54it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all       0.93      0.991\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     30/100      4.04G    0.07863          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.45it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.926      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     31/100      4.04G    0.07794          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.49it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.926      0.991\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     32/100      4.04G    0.07853          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.927      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     33/100      4.04G    0.07621          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.47it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     34/100      4.04G    0.07732          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.49it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.927      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     35/100      4.04G    0.06991          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.923      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     36/100      4.04G    0.07943          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.12it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.21it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.925      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     37/100      4.04G    0.08426          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.927      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     38/100      4.04G    0.07114          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.35it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.927      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     39/100      4.04G    0.06452          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.49it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.928      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     40/100      4.04G     0.0697          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.51it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.928      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     41/100      4.04G    0.06643          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.928      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     42/100      4.04G     0.0681          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.926      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     43/100      4.04G    0.06847          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.928      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     44/100      4.04G    0.06595          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.48it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     45/100      4.04G     0.0644          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.47it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     46/100      4.04G    0.06189          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.16it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.58it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     47/100      4.04G    0.05812          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.49it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     48/100      4.04G    0.05997          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.51it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     49/100      4.04G    0.05563          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.45it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.992\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     50/100      4.04G    0.05417          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.49it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.992\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     51/100      4.04G    0.05683          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     52/100      4.04G    0.05643          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.52it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     53/100      4.04G    0.04896          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.10it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.34it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     54/100      4.04G    0.04852          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.12it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     55/100      4.04G    0.05664          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.11it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.29it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     56/100      4.04G    0.05197          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.11it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.37it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     57/100      4.04G    0.04052          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.12it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.36it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     58/100      4.04G    0.04549          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.11it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     59/100      4.04G     0.0441          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.12it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     60/100      4.04G      0.038          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     61/100      4.04G    0.03916          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     62/100      4.04G    0.03748          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     63/100      4.04G    0.03523          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.52it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     64/100      4.04G    0.03505          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.47it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     65/100      4.04G    0.03209          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.48it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all       0.93      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     66/100      4.04G    0.03422          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     67/100      4.04G    0.03121          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.46it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     68/100      4.04G    0.03267          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.48it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     69/100      4.04G    0.03175          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.11it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.46it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     70/100      4.04G    0.02967          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     71/100      4.04G    0.02566          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     72/100      4.04G    0.02686          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.993\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     73/100      4.04G    0.01994          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.51it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     74/100      4.04G    0.02041          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.48it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     75/100      4.04G    0.01772          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.12it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     76/100      4.04G    0.02013          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.15it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.47it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     77/100      4.04G    0.01959          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.14it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     78/100      4.04G    0.01612          4        224: 100%|██████████| 698/698 [01:53<00:00,  6.13it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.47it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     79/100      4.04G    0.01674          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.11it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.37it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all       0.93      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     80/100      4.04G    0.01484          4        224: 100%|██████████| 698/698 [01:54<00:00,  6.12it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.51it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all       0.93      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     81/100      4.04G    0.01337          4        224: 100%|██████████| 698/698 [01:55<00:00,  6.05it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.09it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.931      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     82/100      4.04G    0.01577          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.98it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.18it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     83/100      4.04G    0.01264          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.99it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.27it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     84/100      4.04G    0.01509          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.99it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.17it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     85/100      4.04G    0.01239          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.99it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.11it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     86/100      4.04G    0.01032          4        224: 100%|██████████| 698/698 [01:57<00:00,  5.95it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.22it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     87/100      4.04G    0.01311          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.99it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     88/100      4.04G    0.01098          4        224: 100%|██████████| 698/698 [01:56<00:00,  6.00it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.20it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     89/100      4.04G    0.01075          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.99it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.17it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     90/100      4.04G    0.01084          4        224: 100%|██████████| 698/698 [01:56<00:00,  6.00it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.18it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     91/100      4.04G   0.008989          4        224: 100%|██████████| 698/698 [01:55<00:00,  6.03it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.08it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     92/100      4.04G   0.007771          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.99it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.13it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     93/100      4.04G   0.008175          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.98it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.11it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     94/100      4.04G   0.008752          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.99it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.14it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     95/100      4.04G   0.008374          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.99it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.08it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     96/100      4.04G   0.007681          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.99it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.20it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     97/100      4.04G   0.008771          4        224: 100%|██████████| 698/698 [01:56<00:00,  6.00it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.14it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     98/100      4.04G   0.006226          4        224: 100%|██████████| 698/698 [01:56<00:00,  6.00it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.16it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     99/100      4.04G    0.00704          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.99it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.08it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem       loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["    100/100      4.04G   0.007238          4        224: 100%|██████████| 698/698 [01:56<00:00,  5.99it/s]\n", "               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.11it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.932      0.994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "100 epochs completed in 3.297 hours.\n", "Optimizer stripped from runs\\classify\\train3\\weights\\last.pt, 72.7MB\n", "Optimizer stripped from runs\\classify\\train3\\weights\\best.pt, 72.7MB\n", "\n", "Validating runs\\classify\\train3\\weights\\best.pt...\n", "Ultralytics 8.3.141  Python-3.12.5 torch-2.6.0+cu118 CUDA:0 (NVIDIA GeForce RTX 3070 Ti Laptop GPU, 8192MiB)\n", "YOLOv8l-cls summary (fused): 54 layers, 36,224,287 parameters, 0 gradients, 98.7 GFLOPs\n", "\u001b[34m\u001b[1mtrain:\u001b[0m C:\\Users\\<USER>\\Desktop\\Disease_prime\\train... found 22308 images in 31 classes  \n", "\u001b[34m\u001b[1mval:\u001b[0m C:\\Users\\<USER>\\Desktop\\Disease_prime\\val... found 1874 images in 31 classes  \n", "\u001b[34m\u001b[1mtest:\u001b[0m C:\\Users\\<USER>\\Desktop\\Disease_prime\\test... found 950 images in 31 classes  \n"]}, {"name": "stderr", "output_type": "stream", "text": ["               classes   top1_acc   top5_acc: 100%|██████████| 30/30 [00:02<00:00, 10.11it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all      0.933      0.994\n", "Speed: 0.1ms preprocess, 1.4ms inference, 0.0ms loss, 0.0ms postprocess per image\n", "Results saved to \u001b[1mruns\\classify\\train3\u001b[0m\n"]}, {"data": {"text/plain": ["ultralytics.utils.metrics.ClassifyMetrics object with attributes:\n", "\n", "confusion_matrix: <ultralytics.utils.metrics.ConfusionMatrix object at 0x000002663BB3B200>\n", "curves: []\n", "curves_results: []\n", "fitness: 0.9637139737606049\n", "keys: ['metrics/accuracy_top1', 'metrics/accuracy_top5']\n", "results_dict: {'metrics/accuracy_top1': 0.9332977533340454, 'metrics/accuracy_top5': 0.9941301941871643, 'fitness': 0.9637139737606049}\n", "save_dir: WindowsPath('runs/classify/train3')\n", "speed: {'preprocess': 0.09304386338223022, 'inference': 1.4481720917887007, 'loss': 0.00018959440312794586, 'postprocess': 0.0004447705143209837}\n", "task: 'classify'\n", "top1: 0.9332977533340454\n", "top5: 0.9941301941871643"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from ultralytics import YOLO\n", "\n", "# Load a classification model (pretrained or empty)\n", "model = YOLO(\"yolov8l-cls.pt\")  # Or 'yolov8s-cls.pt' for smaller size\n", "\n", "# Train the model\n", "model.train(\n", "    data=r\"C:\\Users\\<USER>\\Desktop\\Disease_prime\",  # Folder with train/ and val/\n", "    epochs=100,\n", "    imgsz=224,\n", "    batch=32,\n", "    amp=False\n", ")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}